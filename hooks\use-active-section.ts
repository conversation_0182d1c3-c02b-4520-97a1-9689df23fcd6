"use client"

import { useState, useEffect, useCallback, useRef } from "react"

export function useActiveSection() {
  const [activeSection, setActiveSection] = useState("home")
  const sectionsRef = useRef<string[]>(["home", "about", "skills", "projects", "experience", "contact"])
  const elementsRef = useRef<(HTMLElement | null)[]>([])
  const isScrollingRef = useRef(false)
  const lastScrollTimeRef = useRef(0)

  // Cache section elements for better performance
  const updateSectionElements = useCallback(() => {
    elementsRef.current = sectionsRef.current.map(id => document.getElementById(id))
  }, [])

  // Optimized scroll handler with better performance
  const handleScroll = useCallback(() => {
    if (isScrollingRef.current) return

    isScrollingRef.current = true
    const now = performance.now()

    // Throttle to max 60fps
    if (now - lastScrollTimeRef.current < 16.67) {
      isScrollingRef.current = false
      return
    }

    lastScrollTimeRef.current = now

    requestAnimationFrame(() => {
      // Find the section that's most visible in the viewport
      let currentSection = "home"
      let maxVisibility = 0
      const viewportHeight = window.innerHeight

      elementsRef.current.forEach((element, index) => {
        if (element) {
          const rect = element.getBoundingClientRect()

          // Calculate how much of the section is visible
          const visibleTop = Math.max(0, -rect.top)
          const visibleBottom = Math.min(rect.height, viewportHeight - rect.top)
          const visibleHeight = Math.max(0, visibleBottom - visibleTop)
          const visibility = visibleHeight / viewportHeight

          if (visibility > maxVisibility) {
            maxVisibility = visibility
            currentSection = sectionsRef.current[index]
          }
        }
      })

      setActiveSection(currentSection)
      isScrollingRef.current = false
    })
  }, [])

  useEffect(() => {
    // Initial setup
    updateSectionElements()
    handleScroll()

    // Update section elements on DOM changes
    const handleResize = () => {
      updateSectionElements()
      handleScroll()
    }

    // Use passive listeners for better performance
    window.addEventListener("scroll", handleScroll, { passive: true })
    window.addEventListener("resize", handleResize, { passive: true })

    return () => {
      window.removeEventListener("scroll", handleScroll)
      window.removeEventListener("resize", handleResize)
    }
  }, [handleScroll, updateSectionElements])

  return activeSection
}
