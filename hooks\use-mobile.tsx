import * as React from "react"

const M<PERSON><PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}

// Performance detection hook
export interface PerformanceSettings {
  particleCount: number
  enableComplexAnimations: boolean
  enableCursor: boolean
  targetFPS: number
  enableNebulaEffects: boolean
  enableAuroraEffects: boolean
  enableInteractive3D: boolean
  maxTrailPoints: number
  enableZoomEffect: boolean
}

export function usePerformanceSettings(): PerformanceSettings {
  const [settings, setSettings] = React.useState<PerformanceSettings>({
    particleCount: 1,
    enableComplexAnimations: true,
    enableCursor: true,
    targetFPS: 60,
    enableNebulaEffects: true,
    enableAuroraEffects: true,
    enableInteractive3D: true,
    maxTrailPoints: 8,
    enableZoomEffect: true,
  })

  React.useEffect(() => {
    const detectPerformance = () => {
      const isMobile = window.innerWidth < MOBILE_BREAKPOINT
      const isLowEnd = navigator.hardwareConcurrency <= 4
      const hasLimitedMemory = (navigator as any).deviceMemory && (navigator as any).deviceMemory <= 4
      const isSlowConnection = (navigator as any).connection &&
        ((navigator as any).connection.effectiveType === 'slow-2g' ||
         (navigator as any).connection.effectiveType === '2g' ||
         (navigator as any).connection.effectiveType === '3g')

      // Enhanced performance tier detection
      let performanceTier: 'low' | 'medium' | 'high' = 'high'

      // Check for very low-end devices
      const isVeryLowEnd = navigator.hardwareConcurrency <= 2 ||
        ((navigator as any).deviceMemory && (navigator as any).deviceMemory <= 2)

      if (isVeryLowEnd || (isMobile && isLowEnd)) {
        performanceTier = 'low'
      } else if (isMobile || isLowEnd || hasLimitedMemory || isSlowConnection) {
        performanceTier = 'medium'
      } else if (navigator.hardwareConcurrency <= 8) {
        performanceTier = 'medium'
      }

      // Enhanced settings based on performance tier
      const performanceSettings: Record<string, PerformanceSettings> = {
        low: {
          particleCount: 0.05, // 5% of normal for very low-end
          enableComplexAnimations: false,
          enableCursor: false,
          targetFPS: 30,
          enableNebulaEffects: false,
          enableAuroraEffects: false,
          enableInteractive3D: false,
          maxTrailPoints: 0,
          enableZoomEffect: false,
        },
        medium: {
          particleCount: 0.2, // 20% of normal
          enableComplexAnimations: false, // Disable complex animations on medium
          enableCursor: true,
          targetFPS: 45,
          enableNebulaEffects: false,
          enableAuroraEffects: false, // Disable aurora on medium for better performance
          enableInteractive3D: false,
          maxTrailPoints: 3, // Reduced trail points
          enableZoomEffect: false, // Disable zoom effect on medium
        },
        high: {
          particleCount: 0.8, // 80% of normal for better balance
          enableComplexAnimations: true,
          enableCursor: true,
          targetFPS: 60,
          enableNebulaEffects: true,
          enableAuroraEffects: true,
          enableInteractive3D: true,
          maxTrailPoints: 8, // Restored to 8 for smoother trails
          enableZoomEffect: true,
        }
      }

      setSettings(performanceSettings[performanceTier])
    }

    detectPerformance()

    // Re-detect on resize (orientation change) and visibility change
    window.addEventListener('resize', detectPerformance)
    document.addEventListener('visibilitychange', detectPerformance)

    return () => {
      window.removeEventListener('resize', detectPerformance)
      document.removeEventListener('visibilitychange', detectPerformance)
    }
  }, [])

  return settings
}
