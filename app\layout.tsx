import type React from "react"
import type { Metada<PERSON> } from "next"
import { Poppins, Space_Grotesk } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { BackgroundWrapper } from "@/components/background-wrapper"
import { CosmicCursor } from "@/components/cosmic-cursor"
import { PerformanceMonitorWrapper } from "@/components/performance-monitor-wrapper"

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-poppins",
})

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-space-grotesk",
})

export const metadata: Metadata = {
  title: "Zaid Ahmed S | Portfolio",
  description: "Front-End Developer | UI/UX Enthusiast | Future Full-Stack Engineer",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="dark">
      <body className={`${poppins.variable} ${spaceGrotesk.variable} font-sans relative`}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} disableTransitionOnChange>
          <BackgroundWrapper />
          <CosmicCursor />
          <Navbar />
          {children}
          <Footer />
          <PerformanceMonitorWrapper />
        </ThemeProvider>
      </body>
    </html>
  )
}