"use client"

import { useEffect, useState, useCallback, useRef } from "react"
import { usePerformanceSettings } from "@/hooks/use-mobile"

interface TrailPoint {
  x: number
  y: number
  opacity: number
  id: string
}

export function CosmicCursor() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isVisible, setIsVisible] = useState(false)
  const [trail, setTrail] = useState<TrailPoint[]>([])
  const idCounterRef = useRef(0)
  const lastUpdateRef = useRef(0)
  const performanceSettings = usePerformanceSettings()

  // Generate unique ID for trail points - optimized
  const generateUniqueId = useCallback(() => {
    idCounterRef.current += 1
    return `trail-${idCounterRef.current}`
  }, [])

  // Optimized mouse move handler with immediate response
  const handleMouseMove = useCallback((e: MouseEvent) => {
    // Skip if cursor effects are disabled for performance
    if (!performanceSettings.enableCursor) return

    // Update position immediately for responsive cursor
    setMousePosition({ x: e.clientX, y: e.clientY })
    setIsVisible(true)

    const now = performance.now()
    // Throttle trail updates only, not cursor position
    if (now - lastUpdateRef.current < 8.33) return // 120fps for trail updates
    lastUpdateRef.current = now

    // Add trail point with performance-based max points
    setTrail(prevTrail => {
      const maxTrailPoints = performanceSettings.maxTrailPoints
      if (maxTrailPoints === 0) return []

      const newTrail = [...prevTrail, {
        x: e.clientX,
        y: e.clientY,
        opacity: 1,
        id: generateUniqueId()
      }]

      // Keep only last N trail points based on performance
      return newTrail.slice(-maxTrailPoints)
    })
  }, [performanceSettings.enableCursor, performanceSettings.maxTrailPoints, generateUniqueId])

  const handleMouseLeave = useCallback(() => {
    setIsVisible(false)
  }, [])

  const handleMouseEnter = useCallback(() => {
    setIsVisible(true)
  }, [])

  useEffect(() => {
    // Use passive listeners for better performance
    document.addEventListener("mousemove", handleMouseMove, { passive: true })
    document.addEventListener("mouseleave", handleMouseLeave, { passive: true })
    document.addEventListener("mouseenter", handleMouseEnter, { passive: true })

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseleave", handleMouseLeave)
      document.removeEventListener("mouseenter", handleMouseEnter)
    }
  }, [handleMouseMove, handleMouseLeave, handleMouseEnter])

  // Optimized trail fade out with requestAnimationFrame
  useEffect(() => {
    if (!performanceSettings.enableCursor || performanceSettings.maxTrailPoints === 0) {
      setTrail([])
      return
    }

    let animationFrameId: number
    const fadeTrail = () => {
      setTrail(prevTrail => {
        const updatedTrail = prevTrail.map(point => ({
          ...point,
          opacity: Math.max(0, point.opacity - 0.08) // Slightly faster fade
        })).filter(point => point.opacity > 0.05) // Remove very faint points

        if (updatedTrail.length > 0) {
          animationFrameId = requestAnimationFrame(fadeTrail)
        }
        return updatedTrail
      })
    }

    if (trail.length > 0) {
      animationFrameId = requestAnimationFrame(fadeTrail)
    }

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId)
      }
    }
  }, [trail.length, performanceSettings.enableCursor, performanceSettings.maxTrailPoints])

  // Don't render if cursor effects are disabled or not visible
  if (!isVisible || !performanceSettings.enableCursor) return null

  return (
    <>
      {/* Trail particles */}
      {trail.map((point, index) => (
        <div
          key={point.id}
          className="fixed pointer-events-none z-[9998] cosmic-cursor-trail"
          style={{
            left: point.x - 3,
            top: point.y - 3,
            width: '6px',
            height: '6px',
            background: `radial-gradient(circle, rgba(255,255,255,${point.opacity * 0.8}) 0%, rgba(56,139,253,${point.opacity * 0.6}) 50%, transparent 100%)`,
            borderRadius: '50%',
            opacity: point.opacity * (index / trail.length),
            transform: `scale(${0.3 + (index / trail.length) * 0.7})`,
            boxShadow: `0 0 ${4 + index}px rgba(255,255,255,${point.opacity * 0.5})`,
            filter: 'blur(0.5px)',
            mixBlendMode: 'screen'
          }}
        />
      ))}

      {/* Main cursor */}
      <div
        className="cosmic-cursor"
        style={{
          left: mousePosition.x - 10, // Center the cursor
          top: mousePosition.y - 10,
        }}
      />
    </>
  )
}
