"use client"

import { useEffect, useState } from "react"
import { usePerformanceMonitor, useAdaptiveQuality, usePerformanceBudget } from "@/hooks/use-performance-monitor"
import { usePerformanceSettings } from "@/hooks/use-mobile"

interface PerformanceStats {
  fps: number
  frameTime: number
  quality: number
  particleCount: number
  budgetViolations: number
  isInCriticalState: boolean
  performanceTier: string
}

export function PerformanceMonitor({ showDebug = false }: { showDebug?: boolean }) {
  const { fps, averageFPS, frameTime, isPerformanceGood } = usePerformanceMonitor()
  const { quality } = useAdaptiveQuality()
  const { budgetViolations, isInCriticalState } = usePerformanceBudget()
  const performanceSettings = usePerformanceSettings()
  const [stats, setStats] = useState<PerformanceStats>({
    fps: 60,
    frameTime: 16.67,
    quality: 1,
    particleCount: 1,
    budgetViolations: 0,
    isInCriticalState: false,
    performanceTier: 'high'
  })

  useEffect(() => {
    // Determine performance tier based on settings
    let tier = 'high'
    if (performanceSettings.particleCount <= 0.1) tier = 'low'
    else if (performanceSettings.particleCount <= 0.3) tier = 'medium'

    setStats({
      fps: averageFPS,
      frameTime,
      quality,
      particleCount: performanceSettings.particleCount,
      budgetViolations,
      isInCriticalState,
      performanceTier: tier
    })
  }, [averageFPS, frameTime, quality, performanceSettings.particleCount, budgetViolations, isInCriticalState])

  // Performance validation tests
  const runPerformanceTests = () => {
    const tests = {
      fpsTarget: averageFPS >= 30, // Minimum acceptable FPS
      frameTimeTarget: frameTime <= 33.33, // Maximum acceptable frame time (30fps)
      qualityAdaptation: quality > 0, // Quality should adapt but not go to zero
      budgetCompliance: budgetViolations < 10, // Should not violate budget too often
      responsiveness: !isInCriticalState // Should not be in critical performance state
    }

    return tests
  }

  const performanceTests = runPerformanceTests()
  const allTestsPassed = Object.values(performanceTests).every(test => test)

  if (!showDebug) return null

  return (
    <div className="fixed bottom-4 left-4 z-[10000] bg-black/80 backdrop-blur-xl border border-white/20 rounded-lg p-4 text-white text-xs font-mono max-w-xs">
      <div className="mb-2 font-bold text-primary">Performance Monitor</div>
      
      <div className="space-y-1">
        <div className={`flex justify-between ${isPerformanceGood ? 'text-green-400' : 'text-red-400'}`}>
          <span>FPS:</span>
          <span>{stats.fps.toFixed(1)}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Frame Time:</span>
          <span>{stats.frameTime.toFixed(2)}ms</span>
        </div>
        
        <div className="flex justify-between">
          <span>Quality:</span>
          <span>{(stats.quality * 100).toFixed(0)}%</span>
        </div>
        
        <div className="flex justify-between">
          <span>Particles:</span>
          <span>{(stats.particleCount * 100).toFixed(0)}%</span>
        </div>
        
        <div className="flex justify-between">
          <span>Tier:</span>
          <span className={`capitalize ${
            stats.performanceTier === 'high' ? 'text-green-400' : 
            stats.performanceTier === 'medium' ? 'text-yellow-400' : 'text-red-400'
          }`}>
            {stats.performanceTier}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Budget Violations:</span>
          <span className={stats.budgetViolations > 5 ? 'text-red-400' : 'text-green-400'}>
            {stats.budgetViolations}
          </span>
        </div>
        
        {stats.isInCriticalState && (
          <div className="text-red-400 font-bold">CRITICAL PERFORMANCE</div>
        )}
      </div>

      <div className="mt-3 pt-2 border-t border-white/20">
        <div className="mb-1 font-bold">Performance Tests:</div>
        <div className="space-y-1">
          {Object.entries(performanceTests).map(([test, passed]) => (
            <div key={test} className={`flex justify-between ${passed ? 'text-green-400' : 'text-red-400'}`}>
              <span>{test}:</span>
              <span>{passed ? '✓' : '✗'}</span>
            </div>
          ))}
        </div>
        
        <div className={`mt-2 font-bold ${allTestsPassed ? 'text-green-400' : 'text-red-400'}`}>
          Overall: {allTestsPassed ? 'PASS' : 'FAIL'}
        </div>
      </div>

      <div className="mt-2 text-xs text-white/60">
        Press Ctrl+Shift+P to toggle
      </div>
    </div>
  )
}

// Hook to toggle performance monitor
export function usePerformanceMonitorToggle() {
  const [showDebug, setShowDebug] = useState(false)

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        e.preventDefault()
        setShowDebug(prev => !prev)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  return showDebug
}
