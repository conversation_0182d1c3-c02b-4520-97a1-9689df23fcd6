"use client"

import { useEffect, useRef, useState } from "react"

interface PerformanceMetrics {
  fps: number
  averageFPS: number
  frameTime: number
  isPerformanceGood: boolean
}

export function usePerformanceMonitor(targetFPS: number = 60) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    averageFPS: 60,
    frameTime: 16.67,
    isPerformanceGood: true,
  })

  const frameCountRef = useRef(0)
  const lastTimeRef = useRef(performance.now())
  const fpsHistoryRef = useRef<number[]>([])
  const animationFrameRef = useRef<number>()

  useEffect(() => {
    const measurePerformance = () => {
      const now = performance.now()
      const deltaTime = now - lastTimeRef.current
      
      frameCountRef.current++
      
      // Calculate FPS every second
      if (deltaTime >= 1000) {
        const currentFPS = Math.round((frameCountRef.current * 1000) / deltaTime)
        
        // Keep history of last 10 FPS measurements
        fpsHistoryRef.current.push(currentFPS)
        if (fpsHistoryRef.current.length > 10) {
          fpsHistoryRef.current.shift()
        }
        
        // Calculate average FPS
        const averageFPS = Math.round(
          fpsHistoryRef.current.reduce((sum, fps) => sum + fps, 0) / fpsHistoryRef.current.length
        )
        
        // Determine if performance is good (within 80% of target)
        const isPerformanceGood = averageFPS >= targetFPS * 0.8
        
        setMetrics({
          fps: currentFPS,
          averageFPS,
          frameTime: 1000 / currentFPS,
          isPerformanceGood,
        })
        
        // Reset counters
        frameCountRef.current = 0
        lastTimeRef.current = now
      }
      
      animationFrameRef.current = requestAnimationFrame(measurePerformance)
    }

    animationFrameRef.current = requestAnimationFrame(measurePerformance)

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [targetFPS])

  return metrics
}

// Enhanced adaptive quality with performance budgets
export function useAdaptiveQuality(initialQuality: number = 1) {
  const [quality, setQuality] = useState(initialQuality)
  const [performanceBudget, setPerformanceBudget] = useState({
    targetFPS: 60,
    minFPS: 30,
    maxFrameTime: 16.67, // 60fps = 16.67ms per frame
    criticalFrameTime: 33.33 // 30fps = 33.33ms per frame
  })
  const { isPerformanceGood, averageFPS, frameTime } = usePerformanceMonitor()
  const adjustmentTimeoutRef = useRef<NodeJS.Timeout>()
  const performanceHistoryRef = useRef<number[]>([])

  useEffect(() => {
    // Track performance history
    performanceHistoryRef.current.push(averageFPS)
    if (performanceHistoryRef.current.length > 20) {
      performanceHistoryRef.current.shift()
    }

    // Clear any pending adjustments
    if (adjustmentTimeoutRef.current) {
      clearTimeout(adjustmentTimeoutRef.current)
    }

    // Enhanced quality adjustment logic
    adjustmentTimeoutRef.current = setTimeout(() => {
      const recentPerformance = performanceHistoryRef.current.slice(-5)
      const avgRecentFPS = recentPerformance.reduce((sum, fps) => sum + fps, 0) / recentPerformance.length

      // Critical performance - aggressive quality reduction
      if (frameTime > performanceBudget.criticalFrameTime && quality > 0.1) {
        setQuality(prev => Math.max(0.1, prev - 0.3))
      }
      // Poor performance - moderate quality reduction
      else if (!isPerformanceGood && avgRecentFPS < performanceBudget.minFPS && quality > 0.2) {
        setQuality(prev => Math.max(0.2, prev - 0.2))
      }
      // Below target but acceptable - small quality reduction
      else if (avgRecentFPS < performanceBudget.targetFPS * 0.9 && quality > 0.3) {
        setQuality(prev => Math.max(0.3, prev - 0.1))
      }
      // Good performance - gradual quality increase
      else if (isPerformanceGood && avgRecentFPS > performanceBudget.targetFPS * 0.95 && quality < 1) {
        setQuality(prev => Math.min(1, prev + 0.05))
      }
    }, 1500) // Faster response time

    return () => {
      if (adjustmentTimeoutRef.current) {
        clearTimeout(adjustmentTimeoutRef.current)
      }
    }
  }, [isPerformanceGood, averageFPS, frameTime, quality, performanceBudget])

  return { quality, performanceBudget, setPerformanceBudget }
}

// Performance budget monitoring hook
export function usePerformanceBudget() {
  const [budgetViolations, setBudgetViolations] = useState(0)
  const [isInCriticalState, setIsInCriticalState] = useState(false)
  const { frameTime, averageFPS } = usePerformanceMonitor()
  const violationCountRef = useRef(0)

  useEffect(() => {
    const criticalFrameTime = 33.33 // 30fps
    const targetFrameTime = 16.67 // 60fps

    if (frameTime > criticalFrameTime) {
      violationCountRef.current += 1
      if (violationCountRef.current > 5) {
        setIsInCriticalState(true)
      }
    } else if (frameTime < targetFrameTime) {
      violationCountRef.current = Math.max(0, violationCountRef.current - 1)
      if (violationCountRef.current === 0) {
        setIsInCriticalState(false)
      }
    }

    setBudgetViolations(violationCountRef.current)
  }, [frameTime])

  return { budgetViolations, isInCriticalState }
}
