"use client"

import { useEffect, useRef, useState } from "react"

interface PerformanceMetrics {
  fps: number
  averageFPS: number
  frameTime: number
  isPerformanceGood: boolean
}

export function usePerformanceMonitor(targetFPS: number = 60) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    averageFPS: 60,
    frameTime: 16.67,
    isPerformanceGood: true,
  })

  const frameCountRef = useRef(0)
  const lastTimeRef = useRef(performance.now())
  const fpsHistoryRef = useRef<number[]>([])
  const animationFrameRef = useRef<number>()

  useEffect(() => {
    const measurePerformance = () => {
      const now = performance.now()
      const deltaTime = now - lastTimeRef.current
      
      frameCountRef.current++
      
      // Calculate FPS every second
      if (deltaTime >= 1000) {
        const currentFPS = Math.round((frameCountRef.current * 1000) / deltaTime)
        
        // Keep history of last 10 FPS measurements
        fpsHistoryRef.current.push(currentFPS)
        if (fpsHistoryRef.current.length > 10) {
          fpsHistoryRef.current.shift()
        }
        
        // Calculate average FPS
        const averageFPS = Math.round(
          fpsHistoryRef.current.reduce((sum, fps) => sum + fps, 0) / fpsHistoryRef.current.length
        )
        
        // Determine if performance is good (within 80% of target)
        const isPerformanceGood = averageFPS >= targetFPS * 0.8
        
        setMetrics({
          fps: currentFPS,
          averageFPS,
          frameTime: 1000 / currentFPS,
          isPerformanceGood,
        })
        
        // Reset counters
        frameCountRef.current = 0
        lastTimeRef.current = now
      }
      
      animationFrameRef.current = requestAnimationFrame(measurePerformance)
    }

    animationFrameRef.current = requestAnimationFrame(measurePerformance)

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [targetFPS])

  return metrics
}

// Hook for adaptive quality based on performance
export function useAdaptiveQuality(initialQuality: number = 1) {
  const [quality, setQuality] = useState(initialQuality)
  const { isPerformanceGood, averageFPS } = usePerformanceMonitor()
  const adjustmentTimeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    // Clear any pending adjustments
    if (adjustmentTimeoutRef.current) {
      clearTimeout(adjustmentTimeoutRef.current)
    }

    // Debounce quality adjustments to avoid rapid changes
    adjustmentTimeoutRef.current = setTimeout(() => {
      if (!isPerformanceGood && quality > 0.1) {
        // Reduce quality if performance is poor
        setQuality(prev => Math.max(0.1, prev - 0.1))
      } else if (isPerformanceGood && averageFPS > 55 && quality < 1) {
        // Increase quality if performance is good
        setQuality(prev => Math.min(1, prev + 0.1))
      }
    }, 2000) // Wait 2 seconds before adjusting

    return () => {
      if (adjustmentTimeoutRef.current) {
        clearTimeout(adjustmentTimeoutRef.current)
      }
    }
  }, [isPerformanceGood, averageFPS, quality])

  return quality
}
